<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Order::with(['customer', 'user', 'orderItems.product', 'deliveryInfo', 'payments']);

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->get('status'));
            }

            // Filter by order type
            if ($request->has('order_type')) {
                $query->where('order_type', $request->get('order_type'));
            }

            // Filter by date range
            if ($request->has('date_from')) {
                $query->whereDate('order_date', '>=', $request->get('date_from'));
            }
            if ($request->has('date_to')) {
                $query->whereDate('order_date', '<=', $request->get('date_to'));
            }

            // Search by order number or customer
            if ($request->has('search')) {
                $search = $request->get('search');
                $query->where(function($q) use ($search) {
                    $q->where('order_number', 'like', "%{$search}%")
                      ->orWhereHas('customer', function($customerQuery) use ($search) {
                          $customerQuery->where('fullname_kh', 'like', "%{$search}%")
                                      ->orWhere('phone', 'like', "%{$search}%");
                      });
                });
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'order_date');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $orders = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $orders,
                'message' => 'Orders retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving orders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'customer_id' => 'nullable|exists:info_customers,customer_id',
                'order_type' => 'required|in:pickup,delivery,dine_in',
                'notes' => 'nullable|string',
                'items' => 'required|array|min:1',
                'items.*.product_id' => 'required|exists:products,product_id',
                'items.*.size_id' => 'nullable|exists:product_sizes,size_id',
                'items.*.quantity' => 'required|integer|min:1',
                'items.*.notes' => 'nullable|string'
            ]);

            DB::beginTransaction();

            // Generate order number
            $orderNumber = 'ORD-' . date('Ymd') . '-' . str_pad(Order::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);

            // Calculate totals
            $subtotal = 0;
            $orderItems = [];

            foreach ($validated['items'] as $item) {
                $product = Product::findOrFail($item['product_id']);

                // Check stock
                if ($product->stock_quantity < $item['quantity']) {
                    throw new \Exception("Insufficient stock for product: {$product->name_kh}");
                }

                $unitPrice = $product->price;

                // Add size price adjustment if applicable
                if (isset($item['size_id'])) {
                    $size = $product->sizes()->findOrFail($item['size_id']);
                    $unitPrice += $size->price_adjustment;
                }

                $totalPrice = $unitPrice * $item['quantity'];
                $subtotal += $totalPrice;

                $orderItems[] = [
                    'product_id' => $item['product_id'],
                    'size_id' => $item['size_id'] ?? null,
                    'quantity' => $item['quantity'],
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'notes' => $item['notes'] ?? null
                ];
            }

            $taxAmount = $subtotal * 0.10; // 10% tax
            $deliveryFee = $validated['order_type'] === 'delivery' ? 2.00 : 0.00;
            $totalAmount = $subtotal + $taxAmount + $deliveryFee;

            // Create order
            $order = Order::create([
                'customer_id' => $validated['customer_id'],
                'user_id' => auth()->id(),
                'order_number' => $orderNumber,
                'order_type' => $validated['order_type'],
                'status' => 'pending',
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'delivery_fee' => $deliveryFee,
                'total_amount' => $totalAmount,
                'notes' => $validated['notes'] ?? null,
                'order_date' => now()
            ]);

            // Create order items and update stock
            foreach ($orderItems as $itemData) {
                $itemData['order_id'] = $order->order_id;
                OrderItem::create($itemData);

                // Update product stock
                $product = Product::find($itemData['product_id']);
                $product->decrement('stock_quantity', $itemData['quantity']);
            }

            DB::commit();

            $order->load(['customer', 'orderItems.product', 'orderItems.size']);

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Order created successfully'
            ], 201);

        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error creating order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $order = Order::with(['customer', 'user', 'orderItems.product.category', 'orderItems.size', 'deliveryInfo.driver', 'payments'])
                          ->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Order retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $order = Order::findOrFail($id);

            $validated = $request->validate([
                'status' => 'sometimes|in:pending,preparing,ready,completed,cancelled',
                'notes' => 'nullable|string'
            ]);

            $order->update($validated);
            $order->load(['customer', 'orderItems.product', 'deliveryInfo', 'payments']);

            return response()->json([
                'success' => true,
                'data' => $order,
                'message' => 'Order updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $order = Order::findOrFail($id);

            // Only allow deletion of pending orders
            if ($order->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending orders can be deleted'
                ], 400);
            }

            // Restore stock quantities
            foreach ($order->orderItems as $item) {
                $product = Product::find($item->product_id);
                if ($product) {
                    $product->increment('stock_quantity', $item->quantity);
                }
            }

            $order->delete();

            return response()->json([
                'success' => true,
                'message' => 'Order deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting order: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get order statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $today = now()->startOfDay();
            $thisMonth = now()->startOfMonth();

            $stats = [
                'today' => [
                    'total_orders' => Order::whereDate('order_date', $today)->count(),
                    'total_revenue' => Order::whereDate('order_date', $today)->sum('total_amount'),
                    'pending_orders' => Order::whereDate('order_date', $today)->where('status', 'pending')->count(),
                    'completed_orders' => Order::whereDate('order_date', $today)->where('status', 'completed')->count(),
                ],
                'this_month' => [
                    'total_orders' => Order::where('order_date', '>=', $thisMonth)->count(),
                    'total_revenue' => Order::where('order_date', '>=', $thisMonth)->sum('total_amount'),
                    'avg_order_value' => Order::where('order_date', '>=', $thisMonth)->avg('total_amount'),
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Order statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statistics: ' . $e->getMessage()
            ], 500);
        }
    }
}
