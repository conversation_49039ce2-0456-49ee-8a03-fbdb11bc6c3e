<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\StockAlertService;

class CheckStockAlerts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stock:check-alerts {--force : Force send alerts even if already sent today}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check for low stock products and send Telegram alerts';

    private $stockAlertService;

    public function __construct(StockAlertService $stockAlertService)
    {
        parent::__construct();
        $this->stockAlertService = $stockAlertService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking for low stock products...');

        try {
            if ($this->option('force')) {
                $result = $this->stockAlertService->forceSendStockAlert();
                $this->info('Force checking stock alerts...');
            } else {
                $result = $this->stockAlertService->checkAndSendLowStockAlerts();
            }

            if ($result['success']) {
                $this->info($result['message']);

                if ($result['products_count'] > 0) {
                    $this->warn("Found {$result['products_count']} low stock products:");

                    if (!empty($result['products'])) {
                        $headers = ['Product ID', 'Name (KH)', 'Current Stock', 'Min Level', 'Category'];
                        $rows = [];

                        foreach ($result['products'] as $product) {
                            $rows[] = [
                                $product['product_id'],
                                $product['name_kh'],
                                $product['stock_quantity'],
                                $product['min_stock_level'],
                                $product['category']['name_kh'] ?? 'N/A'
                            ];
                        }

                        $this->table($headers, $rows);
                    }

                    $this->info('Telegram alert sent successfully!');
                } else {
                    $this->info('No low stock products found.');
                }
            } else {
                $this->error($result['message']);
                return 1;
            }

        } catch (\Exception $e) {
            $this->error('Error checking stock alerts: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
