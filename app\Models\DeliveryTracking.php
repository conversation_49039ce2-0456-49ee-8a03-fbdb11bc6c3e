<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DeliveryTracking extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'delivery_tracking';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'tracking_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'delivery_id',
        'status',
        'message',
        'latitude',
        'longitude',
        'address_details',
        'estimated_arrival',
        'photo_url',
        'customer_signature',
        'status_time'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'estimated_arrival' => 'datetime',
        'status_time' => 'datetime'
    ];

    /**
     * Get the delivery that owns the tracking record.
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(DeliveryInfo::class, 'delivery_id', 'delivery_id');
    }
}
