<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\InfoUser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Check if user is authenticated
            $currentUser = $request->user();
            if (!$currentUser) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthenticated'
                ], 401);
            }

            // Check if user has permission to view users
            if (!$currentUser->hasRole('super-admin') && !$currentUser->can('view users')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized. You do not have permission to view users.'
                ], 403);
            }

            // Get users with their roles and permissions
            $users = User::with(['roles', 'permissions'])->get();

            // Transform the data to include role and permission names
            $transformedUsers = $users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'email_verified_at' => $user->email_verified_at,
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                    'roles' => $user->getRoleNames(),
                    'permissions' => $user->getAllPermissions()->pluck('name')
                ];
            });

            return response()->json([
                'status' => 'success',
                'message' => 'Users retrieved successfully',
                'data' => $transformedUsers
            ], 200);

        } catch (\Exception $e) {
            Log::error('User error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve users',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|string|email|max:255|unique:users',
                'password' => 'required|string|min:8',
                'first_name_kh' => 'required|string|max:255',
                'last_name_kh' => 'required|string|max:255',
                'fullname_en' => 'required|string|max:255',
                'gender' => 'required|in:male,female,other',
                'date_of_birth' => 'required|date',
                'phone' => 'required|string|max:20|unique:info_users,phone',
                'current_address' => 'required|string|max:255',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle photo upload if provided
            $photoPath = null;
            if ($request->hasFile('photo')) {
                $photoPath = $request->file('photo')->store('user_photos', 'public');
            }

            // Create the user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
            ]);

            // Create the user info
            $infoUser = InfoUser::create([
                'user_id' => $user->id,
                'first_name_kh' => $request->first_name_kh,
                'last_name_kh' => $request->last_name_kh,
                'fullname_en' => $request->fullname_en,
                'gender' => $request->gender,
                'date_of_birth' => $request->date_of_birth,
                'phone' => $request->phone,
                'current_address' => $request->current_address,
                'photo' => $photoPath,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'User created successfully',
                'data' => [
                    'user' => $user,
                    'info' => $infoUser
                ]
            ], 201);

        } catch (\Exception $e) {
            Log::error('User creation error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create user',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    public function show(Request $request, $id): JsonResponse
    {
        try {
            // Check if user is authenticated
            $currentUser = $request->user();
            if (!$currentUser) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthenticated'
                ], 401);
            }

            // Check if user has permission to view users or is viewing their own profile
            if ($currentUser->id != $id && !$currentUser->hasRole('admin') && !$currentUser->can('view users')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized. You do not have permission to view this user.'
                ], 403);
            }

            $user = User::with(['roles', 'permissions', 'infoUser'])->findOrFail($id);

            $transformedUser = [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'email_verified_at' => $user->email_verified_at,
                'created_at' => $user->created_at,
                'updated_at' => $user->updated_at,
                'roles' => $user->getRoleNames(),
                'permissions' => $user->getAllPermissions()->pluck('name'),
                'info' => $user->infoUser
            ];

            return response()->json([
                'status' => 'success',
                'message' => 'User retrieved successfully',
                'data' => $transformedUser
            ], 200);

        } catch (\Exception $e) {
            Log::error('User show error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, $id): JsonResponse
    {
        try {
            // Check if user is authenticated
            $currentUser = $request->user();
            if (!$currentUser) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthenticated'
                ], 401);
            }

            // Check if user has permission to update users or is updating their own profile
            if ($currentUser->id != $id && !$currentUser->hasRole('admin') && !$currentUser->can('edit users')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized. You do not have permission to update this user.'
                ], 403);
            }

            $user = User::findOrFail($id);
            $infoUser = InfoUser::where('user_id', $id)->first();

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|max:255',
                'email' => 'sometimes|string|email|max:255|unique:users,email,' . $id,
                'password' => 'sometimes|string|min:8',
                'first_name_kh' => 'sometimes|string|max:255',
                'last_name_kh' => 'sometimes|string|max:255',
                'fullname_en' => 'sometimes|string|max:255',
                'gender' => 'sometimes|in:male,female,other',
                'date_of_birth' => 'sometimes|date',
                'phone' => 'sometimes|string|max:20|unique:info_users,phone,' . ($infoUser ? $infoUser->info_id : 'NULL') . ',info_id',
                'current_address' => 'sometimes|string|max:255',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // Handle photo upload if provided
            $photoPath = $infoUser ? $infoUser->photo : null;
            if ($request->hasFile('photo')) {
                // Delete old photo if exists
                if ($photoPath && Storage::disk('public')->exists($photoPath)) {
                    Storage::disk('public')->delete($photoPath);
                }
                $photoPath = $request->file('photo')->store('user_photos', 'public');
            }

            // Update user basic info
            $userUpdateData = [];
            if ($request->has('name')) $userUpdateData['name'] = $request->name;
            if ($request->has('email')) $userUpdateData['email'] = $request->email;
            if ($request->has('password')) $userUpdateData['password'] = Hash::make($request->password);

            if (!empty($userUpdateData)) {
                $user->update($userUpdateData);
            }

            // Update or create user info
            $infoUpdateData = [];
            if ($request->has('first_name_kh')) $infoUpdateData['first_name_kh'] = $request->first_name_kh;
            if ($request->has('last_name_kh')) $infoUpdateData['last_name_kh'] = $request->last_name_kh;
            if ($request->has('fullname_en')) $infoUpdateData['fullname_en'] = $request->fullname_en;
            if ($request->has('gender')) $infoUpdateData['gender'] = $request->gender;
            if ($request->has('date_of_birth')) $infoUpdateData['date_of_birth'] = $request->date_of_birth;
            if ($request->has('phone')) $infoUpdateData['phone'] = $request->phone;
            if ($request->has('current_address')) $infoUpdateData['current_address'] = $request->current_address;
            if ($request->hasFile('photo')) $infoUpdateData['photo'] = $photoPath;

            if (!empty($infoUpdateData)) {
                if ($infoUser) {
                    $infoUser->update($infoUpdateData);
                } else {
                    $infoUpdateData['user_id'] = $user->id;
                    $infoUser = InfoUser::create($infoUpdateData);
                }
            }

            // Reload user with relationships
            $user = User::with(['roles', 'permissions', 'infoUser'])->findOrFail($id);

            return response()->json([
                'status' => 'success',
                'message' => 'User updated successfully',
                'data' => [
                    'user' => $user,
                    'info' => $user->infoUser
                ]
            ], 200);

        } catch (\Exception $e) {
            Log::error('User update error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update user',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            // Check if user is authenticated
            $currentUser = $request->user();
            if (!$currentUser) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthenticated'
                ], 401);
            }

            // Check if user has permission to delete users
            if (!$currentUser->hasRole('admin') && !$currentUser->can('delete users')) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Unauthorized. You do not have permission to delete users.'
                ], 403);
            }

            // Prevent user from deleting themselves
            if ($currentUser->id == $id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'You cannot delete your own account.'
                ], 403);
            }

            $user = User::findOrFail($id);
            $infoUser = InfoUser::where('user_id', $id)->first();

            // Delete photo if exists
            if ($infoUser && $infoUser->photo && Storage::disk('public')->exists($infoUser->photo)) {
                Storage::disk('public')->delete($infoUser->photo);
            }

            // Delete user (InfoUser will be deleted automatically due to cascade)
            $user->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'User deleted successfully'
            ], 200);

        } catch (\Exception $e) {
            Log::error('User deletion error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete user',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
