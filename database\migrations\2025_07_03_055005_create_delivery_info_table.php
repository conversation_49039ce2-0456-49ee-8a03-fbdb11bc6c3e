<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_info', function (Blueprint $table) {
            $table->id('delivery_id');
            $table->foreignId('order_id')->constrained('orders', 'order_id')->onDelete('cascade');
            $table->foreignId('driver_id')->nullable()->constrained('delivery_drivers', 'driver_id')->onDelete('set null');
            $table->text('delivery_address');
            $table->string('customer_phone', 20);
            $table->decimal('delivery_fee', 8, 2);
            $table->enum('delivery_status', ['assigned', 'picked_up', 'on_the_way', 'delivered', 'cancelled'])->default('assigned');
            $table->timestamp('estimated_time')->nullable();
            $table->timestamp('pickup_time')->nullable();
            $table->timestamp('delivery_time')->nullable();
            $table->text('tracking_notes')->nullable();
            $table->timestamps();

            $table->index('order_id');
            $table->index('driver_id');
            $table->index('delivery_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_info');
    }
};
