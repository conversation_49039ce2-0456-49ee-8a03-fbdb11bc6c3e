<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_drivers', function (Blueprint $table) {
            $table->id('driver_id');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('full_name', 100);
            $table->string('phone', 20)->unique();
            $table->enum('vehicle_type', ['motorcycle', 'bicycle', 'car'])->default('motorcycle');
            $table->string('license_plate', 20)->nullable();
            $table->boolean('is_available')->default(true);
            $table->decimal('current_lat', 10, 8)->nullable();
            $table->decimal('current_lng', 11, 8)->nullable();
            $table->timestamps();

            $table->index('user_id');
            $table->index('is_available');
            $table->index(['current_lat', 'current_lng']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_drivers');
    }
};
