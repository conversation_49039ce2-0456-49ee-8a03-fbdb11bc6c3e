<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Authentication Test - <?php echo e(config('app.name')); ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 mb-8">Token-Based Authentication Test</h1>
            
            <!-- Authentication Status -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Authentication Status</h2>
                <div id="authStatus" class="space-y-2">
                    <p><strong>Status:</strong> <span id="status" class="font-mono">Checking...</span></p>
                    <p><strong>Token:</strong> <span id="token" class="font-mono text-sm">Loading...</span></p>
                    <p><strong>User:</strong> <span id="user" class="font-mono text-sm">Loading...</span></p>
                    <p><strong>Roles:</strong> <span id="roles" class="font-mono text-sm">Loading...</span></p>
                    <p><strong>Permissions:</strong> <span id="permissions" class="font-mono text-sm">Loading...</span></p>
                </div>
            </div>

            <!-- Quick Login Form -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Quick Login Test</h2>
                <form id="quickLoginForm" class="space-y-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input type="email" id="loginEmail" value="<EMAIL>" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input type="password" id="loginPassword" value="secret" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium">
                            Login via API
                        </button>
                        <button type="button" id="logoutBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md font-medium">
                            Logout
                        </button>
                        <button type="button" id="refreshBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md font-medium">
                            Refresh Permissions
                        </button>
                    </div>
                </form>
            </div>

            <!-- Permission Tests -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Permission Tests</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2">Role Checks</h3>
                        <div id="roleChecks" class="space-y-1 text-sm">
                            <div>Super Admin: <span id="roleSuper" class="font-mono">-</span></div>
                            <div>Admin: <span id="roleAdmin" class="font-mono">-</span></div>
                            <div>Manager: <span id="roleManager" class="font-mono">-</span></div>
                            <div>User: <span id="roleUser" class="font-mono">-</span></div>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2">Permission Checks</h3>
                        <div id="permissionChecks" class="space-y-1 text-sm">
                            <div>View Users: <span id="permViewUsers" class="font-mono">-</span></div>
                            <div>Create Users: <span id="permCreateUsers" class="font-mono">-</span></div>
                            <div>Delete Users: <span id="permDeleteUsers" class="font-mono">-</span></div>
                            <div>View Dashboard: <span id="permViewDashboard" class="font-mono">-</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Test -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">API Test</h2>
                <div class="space-y-4">
                    <button id="testApiBtn" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md font-medium">
                        Test Protected API Endpoint
                    </button>
                    <div id="apiResult" class="bg-gray-50 p-4 rounded-md">
                        <pre id="apiOutput" class="text-sm text-gray-600">Click the button to test API...</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Auth Library -->
    <script src="/js/auth.js"></script>
    
    <script>
        // Update display with current auth state
        function updateDisplay() {
            const isAuth = Auth.isAuthenticated();
            const token = Auth.getToken();
            const user = Auth.getUser();
            const roles = Auth.getRoles();
            const permissions = Auth.getPermissions();

            // Update status
            document.getElementById('status').textContent = isAuth ? 'Authenticated' : 'Not Authenticated';
            document.getElementById('status').className = `font-mono px-2 py-1 rounded ${isAuth ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800'}`;

            // Update token
            document.getElementById('token').textContent = token ? token.substring(0, 30) + '...' : 'No token';

            // Update user
            document.getElementById('user').textContent = user ? `${user.name} (${user.email})` : 'No user data';

            // Update roles
            document.getElementById('roles').textContent = roles.length > 0 ? roles.join(', ') : 'No roles';

            // Update permissions count
            document.getElementById('permissions').textContent = permissions.length > 0 ? `${permissions.length} permissions` : 'No permissions';

            // Update role checks
            document.getElementById('roleSuper').textContent = Auth.hasRole('super-admin') ? '✅' : '❌';
            document.getElementById('roleAdmin').textContent = Auth.hasRole('admin') ? '✅' : '❌';
            document.getElementById('roleManager').textContent = Auth.hasRole('manager') ? '✅' : '❌';
            document.getElementById('roleUser').textContent = Auth.hasRole('user') ? '✅' : '❌';

            // Update permission checks
            document.getElementById('permViewUsers').textContent = Auth.hasPermission('view users') ? '✅' : '❌';
            document.getElementById('permCreateUsers').textContent = Auth.hasPermission('create users') ? '✅' : '❌';
            document.getElementById('permDeleteUsers').textContent = Auth.hasPermission('delete users') ? '✅' : '❌';
            document.getElementById('permViewDashboard').textContent = Auth.hasPermission('view dashboard') ? '✅' : '❌';
        }

        // Quick login
        document.getElementById('quickLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;
            
            const result = await Auth.login(email, password);
            
            if (result.success) {
                alert('Login successful!');
                updateDisplay();
            } else {
                alert('Login failed: ' + result.message);
            }
        });

        // Logout
        document.getElementById('logoutBtn').addEventListener('click', function() {
            Auth.logout();
            updateDisplay();
            alert('Logged out successfully!');
        });

        // Refresh permissions
        document.getElementById('refreshBtn').addEventListener('click', async function() {
            const result = await Auth.refreshPermissions();
            
            if (result.success) {
                updateDisplay();
                alert('Permissions refreshed!');
            } else {
                alert('Failed to refresh permissions: ' + result.message);
            }
        });

        // Test API
        document.getElementById('testApiBtn').addEventListener('click', async function() {
            try {
                const response = await Auth.apiRequest('/api/user');
                const data = await response.json();
                
                document.getElementById('apiOutput').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('apiOutput').textContent = 'Error: ' + error.message;
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay();
        });

        // Listen for auth state changes
        window.addEventListener('authStateChanged', function(event) {
            console.log('Auth state changed:', event.detail);
            updateDisplay();
        });
    </script>
</body>
</html>
<?php /**PATH D:\MyWorking\Dev+Program\Developer\Still-Backend\own_pos\resources\views/auth-test.blade.php ENDPATH**/ ?>