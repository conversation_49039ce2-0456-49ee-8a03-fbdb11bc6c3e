<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DeliveryInfo;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class DeliveryController extends Controller
{
    /**
     * Display a listing of deliveries.
     */
    public function index(Request $request): JsonResponse
    {
        $query = DeliveryInfo::with(['order', 'driver']);

        // Filter by status if provided
        if ($request->has('status')) {
            $query->where('delivery_status', $request->status);
        }

        // Filter by driver if provided
        if ($request->has('driver_id')) {
            $query->where('driver_id', $request->driver_id);
        }

        $deliveries = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $deliveries
        ]);
    }

    /**
     * Store a newly created delivery.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'order_id' => 'required|exists:orders,order_id',
            'delivery_address' => 'required|string',
            'customer_phone' => 'required|string|max:20',
            'delivery_fee' => 'required|numeric|min:0',
            'estimated_time' => 'nullable|date|after:now',
            'driver_id' => 'nullable|exists:delivery_drivers,driver_id'
        ]);

        $delivery = DeliveryInfo::create(array_merge($validated, [
            'delivery_status' => 'assigned'
        ]));

        $delivery->load(['order', 'driver']);

        return response()->json([
            'success' => true,
            'message' => 'Delivery created successfully',
            'data' => $delivery
        ], 201);
    }

    /**
     * Display the specified delivery.
     */
    public function show(DeliveryInfo $delivery): JsonResponse
    {
        $delivery->load(['order', 'driver', 'order.customer']);

        return response()->json([
            'success' => true,
            'data' => $delivery
        ]);
    }

    /**
     * Update the specified delivery.
     */
    public function update(Request $request, DeliveryInfo $delivery): JsonResponse
    {
        $validated = $request->validate([
            'delivery_address' => 'sometimes|string',
            'customer_phone' => 'nullable|string|max:20',
            'delivery_fee' => 'nullable|numeric|min:0',
            'estimated_time' => 'nullable|date|after:now',
            'driver_id' => 'nullable|exists:delivery_drivers,driver_id'
        ]);

        $delivery->update($validated);
        $delivery->load(['order', 'driver']);

        return response()->json([
            'success' => true,
            'message' => 'Delivery updated successfully',
            'data' => $delivery
        ]);
    }

    /**
     * Remove the specified delivery.
     */
    public function destroy(DeliveryInfo $delivery): JsonResponse
    {
        // Only allow deletion if delivery is still assigned
        if ($delivery->delivery_status !== 'assigned') {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete delivery that is not in assigned status'
            ], 422);
        }

        $delivery->delete();

        return response()->json([
            'success' => true,
            'message' => 'Delivery deleted successfully'
        ]);
    }

    /**
     * Assign a driver to the delivery.
     */
    public function assignDriver(Request $request, DeliveryInfo $delivery): JsonResponse
    {
        $validated = $request->validate([
            'driver_id' => 'required|exists:delivery_drivers,driver_id'
        ]);

        $delivery->update([
            'driver_id' => $validated['driver_id'],
            'delivery_status' => 'assigned'
        ]);

        $delivery->load(['order', 'driver']);

        return response()->json([
            'success' => true,
            'message' => 'Driver assigned successfully',
            'data' => $delivery
        ]);
    }

    /**
     * Update delivery status.
     */
    public function updateStatus(Request $request, DeliveryInfo $delivery): JsonResponse
    {
        $validated = $request->validate([
            'delivery_status' => 'required|in:assigned,picked_up,on_the_way,delivered,cancelled'
        ]);

        $delivery->update($validated);
        $delivery->load(['order', 'driver']);

        return response()->json([
            'success' => true,
            'message' => 'Delivery status updated successfully',
            'data' => $delivery
        ]);
    }

    /**
     * Update delivery location (for tracking).
     */
    public function updateLocation(Request $request, DeliveryInfo $delivery): JsonResponse
    {
        // This method would typically create a new tracking record
        // rather than updating the delivery_info table directly
        $validated = $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'message' => 'nullable|string|max:500',
            'status' => 'required|in:order_confirmed,payment_confirmed,preparing,ready_for_pickup,driver_assigned,picked_up,on_the_way,nearby,arrived,delivered,failed_delivery'
        ]);

        // Create a tracking record
        $delivery->trackingRecords()->create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Delivery location updated successfully',
            'data' => $delivery
        ]);
    }
}
