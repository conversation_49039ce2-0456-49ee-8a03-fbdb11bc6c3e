<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id('payment_id');
            $table->foreignId('order_id')->constrained('orders', 'order_id')->onDelete('cascade');
            $table->enum('payment_method', [
                'cash', 'credit_card', 'debit_card', 'qr_aba', 'qr_acleda',
                'qr_wing', 'mobile_banking', 'bank_transfer'
            ]);
            $table->string('payment_provider', 50)->nullable();
            $table->string('transaction_id', 100)->nullable();
            $table->string('reference_number', 100)->nullable();
            $table->text('qr_code_data')->nullable();
            $table->decimal('amount', 10, 2);
            $table->string('currency', 3)->default('USD');
            $table->decimal('exchange_rate', 10, 4)->default(1.0000);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->text('failure_reason')->nullable();
            $table->timestamp('payment_date')->useCurrent();
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamps();

            $table->index('order_id');
            $table->index('status');
            $table->index('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
