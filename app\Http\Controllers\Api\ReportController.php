<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Payment;
use App\Models\InfoCustomer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Get sales report
     */
    public function salesReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'period' => 'nullable|in:today,yesterday,this_week,last_week,this_month,last_month,this_year',
                'group_by' => 'nullable|in:day,week,month,year'
            ]);

            $dateRange = $this->getDateRange($validated);
            $groupBy = $validated['group_by'] ?? 'day';

            $query = Order::with(['customer', 'orderItems.product'])
                ->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                ->where('status', '!=', 'cancelled');

            // Basic statistics
            $totalRevenue = $query->sum('total_amount');
            $totalOrders = $query->count();
            $avgOrderValue = $totalOrders > 0 ? $totalRevenue / $totalOrders : 0;

            // Orders by status
            $ordersByStatus = $query->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            // Revenue over time
            $revenueOverTime = $this->getRevenueOverTime($dateRange, $groupBy);

            // Top products
            $topProducts = $this->getTopProducts($dateRange, 10);

            // Top customers
            $topCustomers = $this->getTopCustomers($dateRange, 10);

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'start' => $dateRange['start']->format('Y-m-d'),
                        'end' => $dateRange['end']->format('Y-m-d')
                    ],
                    'summary' => [
                        'total_revenue' => round($totalRevenue, 2),
                        'total_orders' => $totalOrders,
                        'average_order_value' => round($avgOrderValue, 2),
                        'orders_by_status' => $ordersByStatus
                    ],
                    'revenue_over_time' => $revenueOverTime,
                    'top_products' => $topProducts,
                    'top_customers' => $topCustomers
                ],
                'message' => 'Sales report generated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating sales report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get inventory report
     */
    public function inventoryReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'category_id' => 'nullable|exists:categories,category_id',
                'low_stock_only' => 'nullable|boolean',
                'out_of_stock_only' => 'nullable|boolean'
            ]);

            $query = Product::with(['category']);

            // Filter by category
            if (!empty($validated['category_id'])) {
                $query->where('category_id', $validated['category_id']);
            }

            // Filter by stock status
            if ($validated['low_stock_only'] ?? false) {
                $query->whereRaw('stock_quantity <= min_stock_level')
                     ->where('min_stock_level', '>', 0);
            } elseif ($validated['out_of_stock_only'] ?? false) {
                $query->where('stock_quantity', 0);
            }

            $products = $query->get();

            // Calculate statistics
            $totalProducts = Product::count();
            $totalValue = Product::selectRaw('SUM(stock_quantity * price) as total_value')->value('total_value');
            $lowStockCount = Product::whereRaw('stock_quantity <= min_stock_level')
                                  ->where('min_stock_level', '>', 0)
                                  ->count();
            $outOfStockCount = Product::where('stock_quantity', 0)->count();

            // Group by category
            $byCategory = Product::with(['category'])
                ->select('category_id',
                        DB::raw('COUNT(*) as product_count'),
                        DB::raw('SUM(stock_quantity) as total_quantity'),
                        DB::raw('SUM(stock_quantity * price) as total_value'))
                ->groupBy('category_id')
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => [
                        'total_products' => $totalProducts,
                        'total_inventory_value' => round($totalValue, 2),
                        'low_stock_products' => $lowStockCount,
                        'out_of_stock_products' => $outOfStockCount
                    ],
                    'products' => $products,
                    'by_category' => $byCategory
                ],
                'message' => 'Inventory report generated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating inventory report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get buy-in/sell-out report
     */
    public function buyInSellOutReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'period' => 'nullable|in:today,yesterday,this_week,last_week,this_month,last_month'
            ]);

            $dateRange = $this->getDateRange($validated);

            // Get products sold during the period
            $soldProducts = OrderItem::with(['product', 'order'])
                ->whereHas('order', function($query) use ($dateRange) {
                    $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                          ->where('status', '!=', 'cancelled');
                })
                ->select('product_id',
                        DB::raw('SUM(quantity) as total_sold'),
                        DB::raw('SUM(quantity * price) as total_revenue'))
                ->groupBy('product_id')
                ->orderBy('total_sold', 'desc')
                ->get();

            // Calculate buy-in costs (assuming cost price is stored or calculated)
            $buyInSellOut = $soldProducts->map(function($item) {
                $product = $item->product;
                $costPrice = $product->cost_price ?? ($product->price * 0.7); // Assume 30% margin if no cost price
                $buyInCost = $item->total_sold * $costPrice;
                $profit = $item->total_revenue - $buyInCost;
                $profitMargin = $item->total_revenue > 0 ? ($profit / $item->total_revenue) * 100 : 0;

                return [
                    'product_id' => $item->product_id,
                    'product_name_kh' => $product->name_kh,
                    'product_name_en' => $product->name_en,
                    'category' => $product->category->name_kh ?? 'N/A',
                    'quantity_sold' => $item->total_sold,
                    'buy_in_cost' => round($buyInCost, 2),
                    'sell_out_revenue' => round($item->total_revenue, 2),
                    'profit' => round($profit, 2),
                    'profit_margin_percent' => round($profitMargin, 2)
                ];
            });

            // Summary calculations
            $totalBuyInCost = $buyInSellOut->sum('buy_in_cost');
            $totalSellOutRevenue = $buyInSellOut->sum('sell_out_revenue');
            $totalProfit = $totalSellOutRevenue - $totalBuyInCost;
            $overallProfitMargin = $totalSellOutRevenue > 0 ? ($totalProfit / $totalSellOutRevenue) * 100 : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'start' => $dateRange['start']->format('Y-m-d'),
                        'end' => $dateRange['end']->format('Y-m-d')
                    ],
                    'summary' => [
                        'total_buy_in_cost' => round($totalBuyInCost, 2),
                        'total_sell_out_revenue' => round($totalSellOutRevenue, 2),
                        'total_profit' => round($totalProfit, 2),
                        'overall_profit_margin_percent' => round($overallProfitMargin, 2),
                        'products_sold' => $buyInSellOut->count()
                    ],
                    'products' => $buyInSellOut
                ],
                'message' => 'Buy-in/Sell-out report generated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating buy-in/sell-out report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customer report
     */
    public function customerReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'period' => 'nullable|in:today,yesterday,this_week,last_week,this_month,last_month'
            ]);

            $dateRange = $this->getDateRange($validated);

            // Customer statistics
            $totalCustomers = InfoCustomer::count();
            $newCustomers = InfoCustomer::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])->count();

            // Top customers by revenue
            $topCustomers = InfoCustomer::with(['orders'])
                ->whereHas('orders', function($query) use ($dateRange) {
                    $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                          ->where('status', '!=', 'cancelled');
                })
                ->withSum(['orders' => function($query) use ($dateRange) {
                    $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                          ->where('status', '!=', 'cancelled');
                }], 'total_amount')
                ->withCount(['orders' => function($query) use ($dateRange) {
                    $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                          ->where('status', '!=', 'cancelled');
                }])
                ->orderBy('orders_sum_total_amount', 'desc')
                ->limit(20)
                ->get();

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'start' => $dateRange['start']->format('Y-m-d'),
                        'end' => $dateRange['end']->format('Y-m-d')
                    ],
                    'summary' => [
                        'total_customers' => $totalCustomers,
                        'new_customers' => $newCustomers,
                        'active_customers' => $topCustomers->count()
                    ],
                    'top_customers' => $topCustomers
                ],
                'message' => 'Customer report generated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error generating customer report: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Helper method to get date range based on period or custom dates
     */
    private function getDateRange(array $validated): array
    {
        if (!empty($validated['date_from']) && !empty($validated['date_to'])) {
            return [
                'start' => Carbon::parse($validated['date_from'])->startOfDay(),
                'end' => Carbon::parse($validated['date_to'])->endOfDay()
            ];
        }

        $period = $validated['period'] ?? 'today';

        switch ($period) {
            case 'today':
                return [
                    'start' => Carbon::today(),
                    'end' => Carbon::today()->endOfDay()
                ];
            case 'yesterday':
                return [
                    'start' => Carbon::yesterday(),
                    'end' => Carbon::yesterday()->endOfDay()
                ];
            case 'this_week':
                return [
                    'start' => Carbon::now()->startOfWeek(),
                    'end' => Carbon::now()->endOfWeek()
                ];
            case 'last_week':
                return [
                    'start' => Carbon::now()->subWeek()->startOfWeek(),
                    'end' => Carbon::now()->subWeek()->endOfWeek()
                ];
            case 'this_month':
                return [
                    'start' => Carbon::now()->startOfMonth(),
                    'end' => Carbon::now()->endOfMonth()
                ];
            case 'last_month':
                return [
                    'start' => Carbon::now()->subMonth()->startOfMonth(),
                    'end' => Carbon::now()->subMonth()->endOfMonth()
                ];
            case 'this_year':
                return [
                    'start' => Carbon::now()->startOfYear(),
                    'end' => Carbon::now()->endOfYear()
                ];
            default:
                return [
                    'start' => Carbon::today(),
                    'end' => Carbon::today()->endOfDay()
                ];
        }
    }

    /**
     * Get revenue over time
     */
    private function getRevenueOverTime(array $dateRange, string $groupBy): array
    {
        $format = match($groupBy) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'year' => '%Y',
            default => '%Y-%m-%d'
        };

        return Order::whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
            ->where('status', '!=', 'cancelled')
            ->select(DB::raw("DATE_FORMAT(created_at, '{$format}') as period"),
                    DB::raw('SUM(total_amount) as revenue'),
                    DB::raw('COUNT(*) as orders'))
            ->groupBy('period')
            ->orderBy('period')
            ->get()
            ->toArray();
    }

    /**
     * Get top products
     */
    private function getTopProducts(array $dateRange, int $limit): array
    {
        return OrderItem::with(['product'])
            ->whereHas('order', function($query) use ($dateRange) {
                $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                      ->where('status', '!=', 'cancelled');
            })
            ->select('product_id',
                    DB::raw('SUM(quantity) as total_quantity'),
                    DB::raw('SUM(quantity * price) as total_revenue'))
            ->groupBy('product_id')
            ->orderBy('total_revenue', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get top customers
     */
    private function getTopCustomers(array $dateRange, int $limit): array
    {
        return InfoCustomer::with(['orders'])
            ->whereHas('orders', function($query) use ($dateRange) {
                $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                      ->where('status', '!=', 'cancelled');
            })
            ->withSum(['orders' => function($query) use ($dateRange) {
                $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                      ->where('status', '!=', 'cancelled');
            }], 'total_amount')
            ->withCount(['orders' => function($query) use ($dateRange) {
                $query->whereBetween('created_at', [$dateRange['start'], $dateRange['end']])
                      ->where('status', '!=', 'cancelled');
            }])
            ->orderBy('orders_sum_total_amount', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }
}
