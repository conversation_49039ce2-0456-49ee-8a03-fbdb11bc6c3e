<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DeliveryDriver extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     */
    protected $table = 'delivery_drivers';

    /**
     * The primary key for the model.
     */
    protected $primaryKey = 'driver_id';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'full_name',
        'phone',
        'vehicle_type',
        'license_plate',
        'is_available',
        'current_lat',
        'current_lng'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_available' => 'boolean',
        'current_lat' => 'decimal:8',
        'current_lng' => 'decimal:8'
    ];

    /**
     * Get the user that owns the driver profile.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the deliveries assigned to this driver.
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(DeliveryInfo::class, 'driver_id', 'driver_id');
    }

    /**
     * Scope a query to only include available drivers.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope a query to only include drivers with a specific vehicle type.
     */
    public function scopeVehicleType($query, $type)
    {
        return $query->where('vehicle_type', $type);
    }
}
