<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class PermissionController extends Controller
{
    /**
     * Display a listing of all permissions.
     */
    public function index(): JsonResponse
    {
        try {
            $permissions = Permission::where('guard_name', 'api')->get();
            
            return response()->json([
                'status' => 'success',
                'message' => 'Permissions retrieved successfully',
                'data' => $permissions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve permissions',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created permission.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:permissions',
                
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $permission = Permission::create([
                'name' => strtolower($request->name),
                'guard_name' => 'api'
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Permission created successfully',
                'data' => $permission,
            ], 201);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create permission',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified permission.
     */
    public function show($id): JsonResponse
    {
        try {
            $permission = Permission::where('guard_name', 'api')->findOrFail($id);
            
            return response()->json([
                'status' => 'success',
                'message' => 'Permission retrieved successfully',
                'data' => $permission
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Permission not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified permission.
     */
    public function update(Request $request, $id): JsonResponse
    {
         try {
            $permission = Permission::where('guard_name', 'api')->findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:permissions,name,'.$id,
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            if (in_array($permission->name, ['create permissions', 'edit permissions', 'delete permissions', 'view permissions'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot modify system permissions'
                ], 403);
            }

            $permission->update([
                'name' => strtolower($request->name),
                'guard_name' => 'api'
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Permission updated successfully',
                'data' => $permission,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update permission',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified permission.
     */
    public function destroy($id)
    {
        try {
            $permission = Permission::where('guard_name', 'api')->findOrFail($id);
            
            if (in_array($permission->name, ['create permissions', 'edit permissions', 'delete permissions', 'view permissions'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot delete system permissions'
                ], 403);
            }

            if ($permission->roles()->count() > 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot delete permission. It is still assigned to roles.',
                    'roles' => $permission->roles->pluck('name')
                ], 400);
            }

            $permission->delete();

            return response()->json([
                'status' => 'success',
                'message' => 'Permission deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete permission',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
