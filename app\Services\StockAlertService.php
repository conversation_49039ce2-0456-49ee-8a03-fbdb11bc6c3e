<?php

namespace App\Services;

use App\Models\Product;
use App\Services\TelegramService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class StockAlertService
{
    private $telegramService;

    public function __construct(TelegramService $telegramService)
    {
        $this->telegramService = $telegramService;
    }

    /**
     * Check for low stock products and send alerts
     */
    public function checkAndSendLowStockAlerts(): array
    {
        try {
            $lowStockProducts = $this->getLowStockProducts();
            
            if (empty($lowStockProducts)) {
                return [
                    'success' => true,
                    'message' => 'No low stock products found',
                    'products_count' => 0
                ];
            }

            // Check if we've already sent an alert today for these products
            $cacheKey = 'low_stock_alert_' . date('Y-m-d');
            $sentProductIds = Cache::get($cacheKey, []);
            
            // Filter out products we've already alerted about today
            $newLowStockProducts = array_filter($lowStockProducts, function($product) use ($sentProductIds) {
                return !in_array($product['product_id'], $sentProductIds);
            });

            if (empty($newLowStockProducts)) {
                return [
                    'success' => true,
                    'message' => 'Low stock alerts already sent today for these products',
                    'products_count' => count($lowStockProducts)
                ];
            }

            // Send Telegram alert
            $alertSent = $this->telegramService->sendStockAlert($newLowStockProducts);

            if ($alertSent) {
                // Cache the product IDs to avoid duplicate alerts today
                $newSentIds = array_merge($sentProductIds, array_column($newLowStockProducts, 'product_id'));
                Cache::put($cacheKey, $newSentIds, now()->endOfDay());

                Log::info('Low stock alert sent successfully', [
                    'products_count' => count($newLowStockProducts),
                    'product_ids' => array_column($newLowStockProducts, 'product_id')
                ]);

                return [
                    'success' => true,
                    'message' => 'Low stock alert sent successfully',
                    'products_count' => count($newLowStockProducts),
                    'products' => $newLowStockProducts
                ];
            } else {
                Log::error('Failed to send low stock alert');
                return [
                    'success' => false,
                    'message' => 'Failed to send low stock alert',
                    'products_count' => count($newLowStockProducts)
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error in stock alert service', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'message' => 'Error checking stock levels: ' . $e->getMessage(),
                'products_count' => 0
            ];
        }
    }

    /**
     * Get products with low stock
     */
    public function getLowStockProducts(): array
    {
        return Product::with(['category'])
            ->whereRaw('stock_quantity <= min_stock_level')
            ->where('min_stock_level', '>', 0) // Only check products with defined minimum levels
            ->get()
            ->toArray();
    }

    /**
     * Check stock for a specific product after order
     */
    public function checkProductStockAfterOrder(string $productId, int $quantityUsed): bool
    {
        try {
            $product = Product::with(['category'])->find($productId);
            
            if (!$product) {
                return false;
            }

            // Check if product is now below minimum stock level
            if ($product->stock_quantity <= $product->min_stock_level && $product->min_stock_level > 0) {
                // Send immediate alert for this specific product
                $alertSent = $this->telegramService->sendStockAlert([$product->toArray()]);
                
                if ($alertSent) {
                    Log::info('Immediate stock alert sent for product after order', [
                        'product_id' => $productId,
                        'current_stock' => $product->stock_quantity,
                        'min_level' => $product->min_stock_level,
                        'quantity_used' => $quantityUsed
                    ]);
                }

                return $alertSent;
            }

            return true;

        } catch (\Exception $e) {
            Log::error('Error checking product stock after order', [
                'product_id' => $productId,
                'quantity_used' => $quantityUsed,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get stock statistics
     */
    public function getStockStatistics(): array
    {
        try {
            $totalProducts = Product::count();
            $lowStockProducts = count($this->getLowStockProducts());
            $outOfStockProducts = Product::where('stock_quantity', 0)->count();
            $wellStockedProducts = $totalProducts - $lowStockProducts - $outOfStockProducts;

            return [
                'total_products' => $totalProducts,
                'well_stocked' => $wellStockedProducts,
                'low_stock' => $lowStockProducts,
                'out_of_stock' => $outOfStockProducts,
                'low_stock_percentage' => $totalProducts > 0 ? round(($lowStockProducts / $totalProducts) * 100, 2) : 0,
                'out_of_stock_percentage' => $totalProducts > 0 ? round(($outOfStockProducts / $totalProducts) * 100, 2) : 0
            ];

        } catch (\Exception $e) {
            Log::error('Error getting stock statistics', ['error' => $e->getMessage()]);
            return [
                'total_products' => 0,
                'well_stocked' => 0,
                'low_stock' => 0,
                'out_of_stock' => 0,
                'low_stock_percentage' => 0,
                'out_of_stock_percentage' => 0
            ];
        }
    }

    /**
     * Send weekly stock report
     */
    public function sendWeeklyStockReport(): bool
    {
        try {
            $stats = $this->getStockStatistics();
            $lowStockProducts = $this->getLowStockProducts();

            $message = "📊 <b>WEEKLY STOCK REPORT</b>\n";
            $message .= "Week ending: " . date('Y-m-d') . "\n\n";
            
            $message .= "<b>Stock Overview:</b>\n";
            $message .= "📦 Total Products: {$stats['total_products']}\n";
            $message .= "✅ Well Stocked: {$stats['well_stocked']}\n";
            $message .= "⚠️ Low Stock: {$stats['low_stock']} ({$stats['low_stock_percentage']}%)\n";
            $message .= "❌ Out of Stock: {$stats['out_of_stock']} ({$stats['out_of_stock_percentage']}%)\n\n";

            if (!empty($lowStockProducts)) {
                $message .= "<b>Products Needing Attention:</b>\n";
                foreach (array_slice($lowStockProducts, 0, 10) as $product) { // Limit to 10 products
                    $message .= "• {$product['name_kh']} (Stock: {$product['stock_quantity']})\n";
                }
                
                if (count($lowStockProducts) > 10) {
                    $message .= "... and " . (count($lowStockProducts) - 10) . " more products\n";
                }
            }

            return $this->telegramService->sendMessage($message);

        } catch (\Exception $e) {
            Log::error('Error sending weekly stock report', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Force send stock alert (bypass daily cache)
     */
    public function forceSendStockAlert(): array
    {
        try {
            $lowStockProducts = $this->getLowStockProducts();
            
            if (empty($lowStockProducts)) {
                return [
                    'success' => true,
                    'message' => 'No low stock products found',
                    'products_count' => 0
                ];
            }

            $alertSent = $this->telegramService->sendStockAlert($lowStockProducts);

            if ($alertSent) {
                Log::info('Forced stock alert sent successfully', [
                    'products_count' => count($lowStockProducts)
                ]);

                return [
                    'success' => true,
                    'message' => 'Stock alert sent successfully',
                    'products_count' => count($lowStockProducts),
                    'products' => $lowStockProducts
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to send stock alert',
                    'products_count' => count($lowStockProducts)
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error in forced stock alert', ['error' => $e->getMessage()]);
            return [
                'success' => false,
                'message' => 'Error sending stock alert: ' . $e->getMessage(),
                'products_count' => 0
            ];
        }
    }
}
