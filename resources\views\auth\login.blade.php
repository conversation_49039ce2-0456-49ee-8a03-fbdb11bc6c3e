<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-blue-500 mb-2">Welcome back</h2>
                <p class="text-gray-600 mb-2">Create a new account</p>
                <p class="text-gray-600 mb-4">OR Sign in with these credentials:</p>
                <div class="text-sm text-gray-500">
                    <p>Email <span class="font-medium"><EMAIL></span></p>
                    <p>Password <span class="font-medium">secret</span></p>
                </div>
            </div>

            <!-- Error Messages -->
            @if ($errors->any())
                <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
                    <ul class="text-sm text-red-600">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <!-- Success Messages -->
            @if (session('success'))
                <div class="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
                    <p class="text-sm text-green-600">{{ session('success') }}</p>
                </div>
            @endif

            <!-- Login Form -->
            <form method="POST" action="{{ route('login') }}" class="space-y-6" id="loginForm">
                @csrf
                
                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input 
                        type="email" 
                        id="email" 
                        name="email" 
                        value="{{ old('email', '<EMAIL>') }}"
                        required 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="<EMAIL>"
                    >
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input 
                        type="password" 
                        id="password" 
                        name="password" 
                        required 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="••••••"
                    >
                </div>

                <!-- Remember Me -->
                <div class="flex items-center">
                    <input 
                        type="checkbox" 
                        id="remember" 
                        name="remember" 
                        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    >
                    <label for="remember" class="ml-2 block text-sm text-gray-700">Remember me</label>
                </div>

                <!-- Submit Button -->
                <div>
                    <button 
                        type="submit" 
                        class="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-200"
                    >
                        SIGN IN
                    </button>
                </div>

                <!-- Footer Links -->
                <div class="text-center space-y-2">
                    <p class="text-sm text-gray-600">
                        Forgot you password? 
                        <a href="#" class="text-blue-500 hover:text-blue-600 font-medium">Reset you password here</a>
                    </p>
                    <p class="text-sm text-gray-600">
                        Don't have an account? 
                        <a href="{{ route('register') }}" class="text-blue-500 hover:text-blue-600 font-medium">Sign up</a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Authentication utility functions
        const Auth = {
            // Store authentication data in sessionStorage
            setAuth(data) {
                sessionStorage.setItem('auth_token', data.token);
                sessionStorage.setItem('user_data', JSON.stringify(data.user));
                sessionStorage.setItem('user_roles', JSON.stringify(data.roles));
                sessionStorage.setItem('user_permissions', JSON.stringify(data.permissions));
                sessionStorage.setItem('auth_timestamp', Date.now().toString());
            },

            // Get authentication token
            getToken() {
                return sessionStorage.getItem('auth_token');
            },

            // Get user data
            getUser() {
                const userData = sessionStorage.getItem('user_data');
                return userData ? JSON.parse(userData) : null;
            },

            // Get user roles
            getRoles() {
                const roles = sessionStorage.getItem('user_roles');
                return roles ? JSON.parse(roles) : [];
            },

            // Get user permissions
            getPermissions() {
                const permissions = sessionStorage.getItem('user_permissions');
                return permissions ? JSON.parse(permissions) : [];
            },

            // Check if user has specific role
            hasRole(role) {
                return this.getRoles().includes(role);
            },

            // Check if user has specific permission
            hasPermission(permission) {
                return this.getPermissions().includes(permission);
            },

            // Check if user is authenticated
            isAuthenticated() {
                return !!this.getToken();
            },

            // Clear authentication data
            logout() {
                sessionStorage.removeItem('auth_token');
                sessionStorage.removeItem('user_data');
                sessionStorage.removeItem('user_roles');
                sessionStorage.removeItem('user_permissions');
                sessionStorage.removeItem('auth_timestamp');
            },

            // Make authenticated API request
            async apiRequest(url, options = {}) {
                const token = this.getToken();
                const defaultOptions = {
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                        ...(token && { 'Authorization': `Bearer ${token}` })
                    }
                };

                const mergedOptions = {
                    ...defaultOptions,
                    ...options,
                    headers: {
                        ...defaultOptions.headers,
                        ...options.headers
                    }
                };

                return fetch(url, mergedOptions);
            }
        };

        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.textContent;

            // Show loading state
            submitButton.textContent = 'SIGNING IN...';
            submitButton.disabled = true;

            try {
                const response = await fetch(this.action, {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': formData.get('_token')
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    // Store authentication data
                    Auth.setAuth(data.data);

                    // Show success message
                    console.log('Login successful!', {
                        user: Auth.getUser(),
                        roles: Auth.getRoles(),
                        permissions: Auth.getPermissions()
                    });

                    // Redirect to dashboard
                    window.location.href = '/dashboard';
                } else {
                    // Show error message
                    alert(data.message || 'Login failed');
                }
            } catch (error) {
                console.error('Login error:', error);
                alert('An error occurred during login');
            } finally {
                // Reset button state
                submitButton.textContent = originalText;
                submitButton.disabled = false;
            }
        });

        // Check if user is already authenticated on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (Auth.isAuthenticated()) {
                console.log('User is already authenticated:', {
                    user: Auth.getUser(),
                    roles: Auth.getRoles(),
                    permissions: Auth.getPermissions()
                });
            }
        });
    </script>
</body>
</html>
