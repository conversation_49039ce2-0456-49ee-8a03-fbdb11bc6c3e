<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('delivery_tracking', function (Blueprint $table) {
            $table->id('tracking_id');
            $table->foreignId('delivery_id')->constrained('delivery_info', 'delivery_id')->onDelete('cascade');
            $table->enum('status', [
                'order_confirmed', 'payment_confirmed', 'preparing', 'ready_for_pickup',
                'driver_assigned', 'picked_up', 'on_the_way', 'nearby', 'arrived',
                'delivered', 'failed_delivery'
            ]);
            $table->string('message')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->text('address_details')->nullable();
            $table->timestamp('estimated_arrival')->nullable();
            $table->string('photo_url')->nullable();
            $table->text('customer_signature')->nullable();
            $table->timestamp('status_time')->useCurrent();
            $table->timestamps();

            $table->index('delivery_id');
            $table->index('status');
            $table->index('status_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('delivery_tracking');
    }
};
