<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Payment::with(['order.customer', 'qrCode', 'card']);

            // Filter by payment method
            if ($request->has('payment_method')) {
                $query->where('payment_method', $request->get('payment_method'));
            }

            // Filter by status
            if ($request->has('status')) {
                $query->where('status', $request->get('status'));
            }

            // Filter by date range
            if ($request->has('date_from')) {
                $query->whereDate('payment_date', '>=', $request->get('date_from'));
            }
            if ($request->has('date_to')) {
                $query->whereDate('payment_date', '<=', $request->get('date_to'));
            }

            // Search by transaction reference
            if ($request->has('search')) {
                $search = $request->get('search');
                $query->where('transaction_reference', 'like', "%{$search}%");
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'payment_date');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $payments = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $payments,
                'message' => 'Payments retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving payments: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'order_id' => 'required|exists:orders,order_id',
                'payment_method' => 'required|in:cash,aba_qr,acleda_qr,wing_qr,card',
                'amount' => 'required|numeric|min:0',
                'qr_code_id' => 'nullable|exists:payment_qr_codes,qr_id',
                'card_id' => 'nullable|exists:payment_cards,card_id',
                'notes' => 'nullable|string'
            ]);

            DB::beginTransaction();

            // Verify order exists and get total amount
            $order = Order::findOrFail($validated['order_id']);

            // Check if payment amount matches order total
            $totalPaid = Payment::where('order_id', $order->order_id)
                              ->where('status', 'completed')
                              ->sum('amount');

            $remainingAmount = $order->total_amount - $totalPaid;

            if ($validated['amount'] > $remainingAmount) {
                throw new \Exception('Payment amount exceeds remaining order balance');
            }

            // Generate transaction reference
            $transactionRef = 'PAY-' . date('Ymd') . '-' . str_pad(Payment::whereDate('created_at', today())->count() + 1, 6, '0', STR_PAD_LEFT);

            $payment = Payment::create([
                'order_id' => $validated['order_id'],
                'payment_method' => $validated['payment_method'],
                'amount' => $validated['amount'],
                'status' => $validated['payment_method'] === 'cash' ? 'completed' : 'pending',
                'transaction_reference' => $transactionRef,
                'qr_code_id' => $validated['qr_code_id'] ?? null,
                'card_id' => $validated['card_id'] ?? null,
                'notes' => $validated['notes'] ?? null,
                'payment_date' => now()
            ]);

            // Update order status if fully paid
            $newTotalPaid = $totalPaid + $validated['amount'];
            if ($newTotalPaid >= $order->total_amount) {
                $order->update(['status' => 'paid']);
            }

            DB::commit();

            $payment->load(['order', 'qrCode', 'card']);

            return response()->json([
                'success' => true,
                'data' => $payment,
                'message' => 'Payment created successfully'
            ], 201);

        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error creating payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $payment = Payment::with(['order.customer', 'qrCode', 'card'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $payment,
                'message' => 'Payment retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment not found'
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $payment = Payment::findOrFail($id);

            $validated = $request->validate([
                'status' => 'sometimes|in:pending,completed,failed,refunded',
                'notes' => 'nullable|string'
            ]);

            $payment->update($validated);
            $payment->load(['order', 'qrCode', 'card']);

            return response()->json([
                'success' => true,
                'data' => $payment,
                'message' => 'Payment updated successfully'
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $payment = Payment::findOrFail($id);

            // Only allow deletion of pending payments
            if ($payment->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending payments can be deleted'
                ], 400);
            }

            $payment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Payment deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting payment: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process payment (confirm/complete)
     */
    public function processPayment(Request $request, string $id): JsonResponse
    {
        try {
            $payment = Payment::findOrFail($id);

            $validated = $request->validate([
                'action' => 'required|in:confirm,fail,refund',
                'notes' => 'nullable|string'
            ]);

            DB::beginTransaction();

            switch ($validated['action']) {
                case 'confirm':
                    $payment->update([
                        'status' => 'completed',
                        'notes' => $validated['notes'] ?? $payment->notes
                    ]);

                    // Check if order is fully paid
                    $order = $payment->order;
                    $totalPaid = Payment::where('order_id', $order->order_id)
                                      ->where('status', 'completed')
                                      ->sum('amount');

                    if ($totalPaid >= $order->total_amount) {
                        $order->update(['status' => 'paid']);
                    }
                    break;

                case 'fail':
                    $payment->update([
                        'status' => 'failed',
                        'notes' => $validated['notes'] ?? $payment->notes
                    ]);
                    break;

                case 'refund':
                    $payment->update([
                        'status' => 'refunded',
                        'notes' => $validated['notes'] ?? $payment->notes
                    ]);
                    break;
            }

            DB::commit();

            $payment->load(['order', 'qrCode', 'card']);

            return response()->json([
                'success' => true,
                'data' => $payment,
                'message' => 'Payment processed successfully'
            ]);

        } catch (ValidationException $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error processing payment: ' . $e->getMessage()
            ], 500);
        }
    }
}
