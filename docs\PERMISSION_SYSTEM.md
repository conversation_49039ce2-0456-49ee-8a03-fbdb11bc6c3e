# Permission System Documentation

## Overview
This POS system uses the Spatie <PERSON>vel Permission package for role-based access control (RBAC). The system provides comprehensive permission management through the `PermissionController`.

## API Endpoints

### Permission Management

#### List Permissions
```
GET /api/permissions
```
Query Parameters:
- `guard_name` (optional): Filter by guard name
- `search` (optional): Search permissions by name
- `per_page` (optional): Number of items per page (default: 15)

#### Create Permission
```
POST /api/permissions
```
Body:
```json
{
    "name": "manage products",
    "guard_name": "web"
}
```

#### Get Permission
```
GET /api/permissions/{permission}
```

#### Update Permission
```
PUT /api/permissions/{permission}
```

#### Delete Permission
```
DELETE /api/permissions/{permission}
```

#### Bulk Create Permissions
```
POST /api/permissions/bulk-create
```
Body:
```json
{
    "permissions": [
        {"name": "view reports", "guard_name": "web"},
        {"name": "export data", "guard_name": "web"}
    ]
}
```

### Role Management

#### List Roles
```
GET /api/permissions/roles/list
```

#### Create Role
```
POST /api/permissions/roles
```
Body:
```json
{
    "name": "Store Manager",
    "guard_name": "web",
    "permissions": [1, 2, 3]
}
```

#### Get Role
```
GET /api/permissions/roles/{role}
```

#### Update Role
```
PUT /api/permissions/roles/{role}
```

#### Delete Role
```
DELETE /api/permissions/roles/{role}
```

### Role-Permission Management

#### Assign Permissions to Role
```
POST /api/permissions/roles/{role}/assign-permissions
```
Body:
```json
{
    "permissions": [1, 2, 3, 4]
}
```

#### Remove Permissions from Role
```
POST /api/permissions/roles/{role}/remove-permissions
```

### User-Role Management

#### Assign Roles to User
```
POST /api/permissions/users/{user}/assign-roles
```
Body:
```json
{
    "roles": [1, 2]
}
```

#### Remove Roles from User
```
POST /api/permissions/users/{user}/remove-roles
```

### User-Permission Management

#### Assign Permissions to User
```
POST /api/permissions/users/{user}/assign-permissions
```

#### Remove Permissions from User
```
POST /api/permissions/users/{user}/remove-permissions
```

#### Get User Permissions
```
GET /api/permissions/users/{user}/permissions
```

#### Check User Permission
```
POST /api/permissions/users/{user}/check-permission
```
Body:
```json
{
    "permission": "manage products"
}
```

#### Check User Role
```
POST /api/permissions/users/{user}/check-role
```
Body:
```json
{
    "role": "Admin"
}
```

#### Get Users with Permissions
```
GET /api/permissions/users/with-permissions
```
Query Parameters:
- `role` (optional): Filter by role
- `permission` (optional): Filter by permission

## Default Roles and Permissions

### Roles
1. **Super Admin** - All permissions
2. **Admin** - Most management permissions
3. **Manager** - Store management permissions
4. **Cashier** - Basic POS operations
5. **Delivery Driver** - Delivery tracking only
6. **Inventory Staff** - Inventory management

### Permission Categories
- **Product Management**: view, create, edit, delete products
- **Order Management**: view, create, edit, process orders
- **Customer Management**: view, create, edit customers
- **Payment Management**: view, process payments
- **Delivery Management**: view, create, track deliveries
- **User Management**: manage users and permissions
- **Reports**: view various reports
- **Notifications**: send notifications
- **System Administration**: system settings
- **Financial Management**: pricing, profits
- **Inventory Management**: stock management

## Setup Instructions

1. Run the permission seeder:
```bash
php artisan db:seed --class=PermissionSeeder
```

2. Assign roles to users in your application:
```php
$user = User::find(1);
$user->assignRole('Admin');
```

3. Check permissions in your controllers:
```php
if ($user->hasPermissionTo('manage products')) {
    // User can manage products
}

if ($user->hasRole('Admin')) {
    // User is an admin
}
```

## Middleware Usage

You can protect routes using the permission middleware:

```php
Route::middleware(['permission:manage products'])->group(function () {
    // Protected routes
});

Route::middleware(['role:Admin'])->group(function () {
    // Admin only routes
});
```

## Best Practices

1. Use descriptive permission names
2. Group related permissions logically
3. Assign permissions to roles, not directly to users
4. Regularly audit user permissions
5. Use the least privilege principle
6. Test permission changes thoroughly
