<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>Checking Authentication - <?php echo e(config('app.name')); ?></title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .loader {
            text-align: center;
            color: white;
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .message {
            font-size: 18px;
            margin-bottom: 10px;
        }
        .sub-message {
            font-size: 14px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="loader">
        <div class="spinner"></div>
        <div class="message">Checking Authentication...</div>
        <div class="sub-message">Please wait while we verify your session</div>
    </div>

    <script>
        // Authentication utility functions
        const Auth = {
            getToken() {
                return sessionStorage.getItem('auth_token');
            },
            getUser() {
                const userData = sessionStorage.getItem('user_data');
                return userData ? JSON.parse(userData) : null;
            },
            isAuthenticated() {
                return !!this.getToken();
            },
            isExpired(maxAgeMinutes = 480) { // 8 hours default
                const timestamp = sessionStorage.getItem('auth_timestamp');
                if (!timestamp) return true;
                
                const now = Date.now();
                const maxAge = maxAgeMinutes * 60 * 1000;
                return (now - parseInt(timestamp)) > maxAge;
            },
            async validateToken() {
                const token = this.getToken();
                if (!token) return false;

                try {
                    const response = await fetch('/api/user', {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Accept': 'application/json',
                            'Content-Type': 'application/json'
                        }
                    });

                    return response.ok;
                } catch (error) {
                    console.error('Token validation failed:', error);
                    return false;
                }
            },
            clearAuth() {
                sessionStorage.removeItem('auth_token');
                sessionStorage.removeItem('user_data');
                sessionStorage.removeItem('user_roles');
                sessionStorage.removeItem('user_permissions');
                sessionStorage.removeItem('auth_timestamp');
            }
        };

        // Check authentication and redirect accordingly
        async function checkAuthAndRedirect() {
            const originalUrl = '<?php echo e($originalUrl); ?>';
            const isHomePage = originalUrl === '/' || originalUrl.includes('127.0.0.1:8000/');
            const isLoginPage = originalUrl.includes('/login');
            const isRegisterPage = originalUrl.includes('/register');

            console.log('Checking authentication for:', originalUrl);

            // Step 1: Check if we have a token in sessionStorage
            if (Auth.isAuthenticated()) {
                console.log('Token found in sessionStorage');

                // Step 2: Check if token is expired
                if (Auth.isExpired()) {
                    console.log('Token is expired, clearing sessionStorage');
                    Auth.clearAuth();
                } else {
                    // Step 3: Validate token with server
                    console.log('Validating token with server...');
                    const isValid = await Auth.validateToken();

                    if (isValid) {
                        console.log('Token is valid, redirecting to dashboard');
                        // Token is valid, redirect to dashboard
                        window.location.href = '/dashboard';
                        return;
                    } else {
                        console.log('Token is invalid, clearing sessionStorage');
                        Auth.clearAuth();
                    }
                }
            }

            // Step 4: No valid token, check Laravel session
            console.log('No valid token, checking Laravel session...');

            try {
                const response = await fetch('/api/user', {
                    credentials: 'include', // Include cookies for session auth
                    headers: {
                        'Accept': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    }
                });

                if (response.ok) {
                    console.log('Laravel session is valid, redirecting to dashboard');
                    // Laravel session is valid, redirect to dashboard
                    window.location.href = '/dashboard';
                    return;
                }
            } catch (error) {
                console.log('Laravel session check failed:', error);
            }

            // Step 5: No authentication found, proceed based on original URL
            console.log('No authentication found, proceeding to:', originalUrl);

            if (isLoginPage) {
                // Load login page content
                window.location.href = '/login?direct=true';
            } else if (isRegisterPage) {
                // Load register page content
                window.location.href = '/register?direct=true';
            } else {
                // For home page or other pages, redirect to login
                window.location.href = '/login?direct=true';
            }
        }

        // Start the authentication check
        document.addEventListener('DOMContentLoaded', function() {
            // Small delay to ensure page is ready
            setTimeout(checkAuthAndRedirect, 100);
        });

        // Fallback timeout in case something goes wrong
        setTimeout(function() {
            console.warn('Authentication check timeout, redirecting to login');
            window.location.href = '/login?direct=true';
        }, 5000); // 5 second timeout
    </script>
</body>
</html>
<?php /**PATH D:\MyWorking\Dev+Program\Developer\Still-Backend\own_pos\resources\views/auth/check-session-storage.blade.php ENDPATH**/ ?>